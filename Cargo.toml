[package]
name = "docx-template-processor"
version = "0.1.0"
edition = "2021"

[dependencies]
# CSV处理
csv = "1.3"
# YAML配置文件处理
serde = { version = "1.0", features = ["derive"] }
serde_yaml = "0.9"
# 日期时间处理
chrono = "0.4"
# 错误处理
anyhow = "1.0"
thiserror = "1.0"
# 文件系统操作
walkdir = "2.4"
# 字符串处理
regex = "1.10"
# DOCX文件处理
zip = "0.6"
quick-xml = { version = "0.31", features = ["serialize"] }
# 编码处理
encoding_rs = "0.8"
