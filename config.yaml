csv_file: "2025.csv"
template_dir: "模板"
output_dir: "输出"

# CSV列名到模板变量的映射
column_mappings:
  故障时间: "故障时间"
  所属部门: "所属部门"
  项目名称: "项目名称"
  所属科室: "所属科室"
  业务系统: "业务系统"
  IP地址: "IP地址"
  设备型号: "设备型号"
  坏件类型: "坏件类型"
  坏件状态: "坏件状态"
  坏件PN号: "坏件PN号"
  坏件SN号: "坏件SN号"
  坏件参数: "坏件参数"
  设备位置（从下往上）: "设备位置"
  报障人: "报障人"
  系统责任人: "系统责任人"
  联系电话: "联系电话"
  备件PN号: "备件PN号"
  备件SN号: "备件SN号"
  更换时间: "更换时间"
  更换人: "更换人"

# 目录路径中的占位符映射
path_mappings:
  年: "年"
  月: "月"
  日: "日"
  单位: "所属部门"
  位置: "设备位置"
  机型: "设备型号"
  故障类型: "坏件类型"

# 日期格式配置
date_format:
  input_format: "%Y%m%d"
  output_format: "%Y年%m月%d日"
  date_columns:
    - "故障时间"
    - "更换时间"
