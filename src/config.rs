use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct Config {
    /// CSV文件路径
    pub csv_file: String,
    /// 模板根目录路径
    pub template_dir: String,
    /// 输出根目录路径
    pub output_dir: String,
    /// CSV列名到模板变量的映射
    pub column_mappings: HashMap<String, String>,
    /// 目录路径中的占位符映射
    pub path_mappings: HashMap<String, String>,
    /// 日期格式配置
    pub date_format: DateFormatConfig,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct DateFormatConfig {
    /// 输入日期格式（如："%Y%m%d"）
    pub input_format: String,
    /// 输出日期格式（如："%Y年%m月%d日"）
    pub output_format: String,
    /// 日期字段列表
    pub date_columns: Vec<String>,
}

impl Default for Config {
    fn default() -> Self {
        let mut column_mappings = HashMap::new();
        column_mappings.insert("故障时间".to_string(), "故障时间".to_string());
        column_mappings.insert("所属部门".to_string(), "所属部门".to_string());
        column_mappings.insert("项目名称".to_string(), "项目名称".to_string());
        column_mappings.insert("所属科室".to_string(), "所属科室".to_string());
        column_mappings.insert("业务系统".to_string(), "业务系统".to_string());
        column_mappings.insert("IP地址".to_string(), "IP地址".to_string());
        column_mappings.insert("设备型号".to_string(), "设备型号".to_string());
        column_mappings.insert("坏件类型".to_string(), "坏件类型".to_string());
        column_mappings.insert("坏件状态".to_string(), "坏件状态".to_string());
        column_mappings.insert("坏件PN号".to_string(), "坏件PN号".to_string());
        column_mappings.insert("坏件SN号".to_string(), "坏件SN号".to_string());
        column_mappings.insert("坏件参数".to_string(), "坏件参数".to_string());
        column_mappings.insert("设备位置（从下往上）".to_string(), "设备位置".to_string());
        column_mappings.insert("报障人".to_string(), "报障人".to_string());
        column_mappings.insert("系统责任人".to_string(), "系统责任人".to_string());
        column_mappings.insert("联系电话".to_string(), "联系电话".to_string());
        column_mappings.insert("备件PN号".to_string(), "备件PN号".to_string());
        column_mappings.insert("备件SN号".to_string(), "备件SN号".to_string());
        column_mappings.insert("更换时间".to_string(), "更换时间".to_string());
        column_mappings.insert("更换人".to_string(), "更换人".to_string());

        let mut path_mappings = HashMap::new();
        path_mappings.insert("年".to_string(), "年".to_string());
        path_mappings.insert("月".to_string(), "月".to_string());
        path_mappings.insert("日".to_string(), "日".to_string());
        path_mappings.insert("单位".to_string(), "所属部门".to_string());
        path_mappings.insert("位置".to_string(), "设备位置".to_string());
        path_mappings.insert("机型".to_string(), "设备型号".to_string());
        path_mappings.insert("故障类型".to_string(), "坏件类型".to_string());

        Self {
            csv_file: "2025.csv".to_string(),
            template_dir: "模板".to_string(),
            output_dir: "输出".to_string(),
            column_mappings,
            path_mappings,
            date_format: DateFormatConfig {
                input_format: "%Y%m%d".to_string(),
                output_format: "%Y年%m月%d日".to_string(),
                date_columns: vec!["故障时间".to_string(), "更换时间".to_string()],
            },
        }
    }
}

impl Config {
    pub fn load_from_file(path: &str) -> anyhow::Result<Self> {
        let content = std::fs::read_to_string(path)?;
        let config: Config = serde_yaml::from_str(&content)?;
        Ok(config)
    }

    pub fn save_to_file(&self, path: &str) -> anyhow::Result<()> {
        let content = serde_yaml::to_string(self)?;
        std::fs::write(path, content)?;
        Ok(())
    }
}
