mod config;
mod csv_processor;
mod docx_processor;
mod path_processor;

use anyhow::{Context, Result};
use config::Config;
use csv_processor::CsvProcessor;
use docx_processor::DocxProcessor;
use path_processor::PathProcessor;
use std::env;

fn main() -> Result<()> {
    // 获取配置文件路径
    let config_path = env::args()
        .nth(1)
        .unwrap_or_else(|| "config.yaml".to_string());

    // 加载配置
    let config = if std::path::Path::new(&config_path).exists() {
        Config::load_from_file(&config_path)
            .with_context(|| format!("无法加载配置文件: {}", config_path))?
    } else {
        println!("配置文件不存在，创建默认配置文件: {}", config_path);
        let default_config = Config::default();
        default_config.save_to_file(&config_path)
            .with_context(|| format!("无法保存默认配置文件: {}", config_path))?;
        default_config
    };

    println!("使用配置文件: {}", config_path);
    println!("CSV文件: {}", config.csv_file);
    println!("模板目录: {}", config.template_dir);
    println!("输出目录: {}", config.output_dir);

    // 初始化处理器
    let csv_processor = CsvProcessor::new(config.clone());
    let docx_processor = DocxProcessor::new();
    let path_processor = PathProcessor::new(config.clone());

    // 读取CSV数据
    println!("正在读取CSV文件...");
    let records = csv_processor.read_csv()
        .with_context(|| "读取CSV文件失败")?;
    
    println!("成功读取 {} 条记录", records.len());

    // 处理每条记录
    for (index, record) in records.iter().enumerate() {
        println!("正在处理第 {} 条记录...", index + 1);
        
        // 提取日期组件
        let date_components = csv_processor.extract_date_components(record);
        
        // 生成输出文件路径映射
        let file_mappings = path_processor.generate_output_structure(record, &date_components)
            .with_context(|| format!("生成第 {} 条记录的输出路径失败", index + 1))?;
        
        // 创建输出目录
        path_processor.create_output_directories(&file_mappings)
            .with_context(|| format!("创建第 {} 条记录的输出目录失败", index + 1))?;
        
        // 构建替换映射
        let replacements = path_processor.build_replacements(record, &date_components);
        
        // 处理每个模板文件
        for (template_path, output_path) in file_mappings {
            println!("  处理模板: {} -> {}", 
                template_path.display(), 
                output_path.display()
            );
            
            docx_processor.process_template(
                &template_path.to_string_lossy(),
                &output_path.to_string_lossy(),
                &replacements,
            ).with_context(|| {
                format!("处理模板文件失败: {} -> {}", 
                    template_path.display(), 
                    output_path.display()
                )
            })?;
        }
    }

    println!("所有文件处理完成！");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[test]
    fn test_config_creation() {
        let config = Config::default();
        assert_eq!(config.csv_file, "2025.csv");
        assert_eq!(config.template_dir, "模板");
        assert_eq!(config.output_dir, "输出");
    }

    #[test]
    fn test_date_format() {
        let config = Config::default();
        let csv_processor = CsvProcessor::new(config);
        
        let formatted = csv_processor.format_date("20250506").unwrap();
        assert_eq!(formatted, "2025年5月6日");
    }
}
