use anyhow::{Context, Result};
// use quick_xml::events::{BytesEnd, BytesStart, BytesText, Event};
// use quick_xml::{Reader, Writer};
use regex::Regex;
use std::collections::HashMap;
use std::fs::File;
use std::io::{BufReader, Write};
use zip::{ZipArchive, ZipWriter};

pub struct DocxProcessor;

impl DocxProcessor {
    pub fn new() -> Self {
        Self
    }

    pub fn process_template(
        &self,
        template_path: &str,
        output_path: &str,
        replacements: &HashMap<String, String>,
    ) -> Result<()> {
        // 读取DOCX文件
        let file = File::open(template_path)
            .with_context(|| format!("无法打开模板文件: {}", template_path))?;
        let mut archive = ZipArchive::new(BufReader::new(file))?;

        // 创建输出文件
        let output_file = File::create(output_path)
            .with_context(|| format!("无法创建输出文件: {}", output_path))?;
        let mut zip_writer = ZipWriter::new(output_file);

        // 处理ZIP文件中的每个条目
        for i in 0..archive.len() {
            let mut file = archive.by_index(i)?;
            let file_name = file.name().to_string();

            // 开始写入新的ZIP条目
            zip_writer.start_file(&file_name, zip::write::FileOptions::default())?;

            if file_name == "word/document.xml" {
                // 处理主文档XML
                let mut content = String::new();
                std::io::Read::read_to_string(&mut file, &mut content)?;
                let processed_content = self.replace_placeholders(&content, replacements)?;
                zip_writer.write_all(processed_content.as_bytes())?;
            } else {
                // 直接复制其他文件
                std::io::copy(&mut file, &mut zip_writer)?;
            }
        }

        zip_writer.finish()?;
        Ok(())
    }

    fn replace_placeholders(
        &self,
        xml_content: &str,
        replacements: &HashMap<String, String>,
    ) -> Result<String> {
        let mut result = xml_content.to_string();

        // 首先处理被分割的占位符
        // 这种方法会查找所有可能的占位符模式，包括被XML标签分割的

        // 步骤1: 找到所有可能的占位符开始位置
        let mut placeholder_starts = Vec::new();
        let chars: Vec<char> = result.chars().collect();

        for (i, &ch) in chars.iter().enumerate() {
            if ch == '{' {
                placeholder_starts.push(i);
            }
        }

        // 步骤2: 对于每个开始位置，尝试找到对应的结束位置
        for &start in &placeholder_starts {
            if let Some(end) = self.find_placeholder_end(&chars, start) {
                // 提取占位符内容（包括XML标签）
                let placeholder_with_xml: String = chars[start..=end].iter().collect();

                // 提取纯文本内容
                let placeholder_text = self.extract_placeholder_text(&placeholder_with_xml);

                if !placeholder_text.is_empty() {
                    // 查找替换值
                    if let Some(replacement_value) = replacements.get(&placeholder_text) {
                        // 替换整个占位符（包括XML标签）
                        result = result.replace(&placeholder_with_xml, replacement_value);
                    } else {
                        // 如果没有找到替换值，替换为空字符串
                        println!("Warning: No replacement found for placeholder: {}", placeholder_text);
                        result = result.replace(&placeholder_with_xml, "");
                    }
                }
            }
        }

        Ok(result)
    }

    fn find_placeholder_end(&self, chars: &[char], start: usize) -> Option<usize> {
        let mut depth = 0;
        let mut in_tag = false;

        for i in start..chars.len() {
            match chars[i] {
                '{' if !in_tag => depth += 1,
                '}' if !in_tag => {
                    depth -= 1;
                    if depth == 0 {
                        return Some(i);
                    }
                }
                '<' => in_tag = true,
                '>' => in_tag = false,
                _ => {}
            }
        }
        None
    }

    fn extract_placeholder_text(&self, placeholder_with_xml: &str) -> String {
        // 移除XML标签，只保留文本内容
        let mut result = String::new();
        let mut in_tag = false;
        let mut chars = placeholder_with_xml.chars().peekable();

        while let Some(ch) = chars.next() {
            match ch {
                '<' => in_tag = true,
                '>' => in_tag = false,
                _ if !in_tag => result.push(ch),
                _ => {}
            }
        }

        // 移除开头和结尾的大括号，并清理空白
        result = result.trim().to_string();
        if result.starts_with('{') && result.ends_with('}') {
            result = result[1..result.len()-1].trim().to_string();
        }

        result
    }

    pub fn extract_placeholders(&self, template_path: &str) -> Result<Vec<String>> {
        let file = File::open(template_path)?;
        let mut archive = ZipArchive::new(BufReader::new(file))?;
        
        // 查找document.xml文件
        let mut document_xml = None;
        for i in 0..archive.len() {
            let file = archive.by_index(i)?;
            if file.name() == "word/document.xml" {
                document_xml = Some(i);
                break;
            }
        }

        if let Some(index) = document_xml {
            let mut file = archive.by_index(index)?;
            let mut content = String::new();
            std::io::Read::read_to_string(&mut file, &mut content)?;
            
            let placeholder_regex = Regex::new(r"\{\s*([^}]+)\s*\}")?;
            let mut placeholders = Vec::new();
            
            for caps in placeholder_regex.captures_iter(&content) {
                let placeholder = caps.get(1).unwrap().as_str().trim().to_string();
                if !placeholders.contains(&placeholder) {
                    placeholders.push(placeholder);
                }
            }
            
            Ok(placeholders)
        } else {
            Ok(Vec::new())
        }
    }
}
