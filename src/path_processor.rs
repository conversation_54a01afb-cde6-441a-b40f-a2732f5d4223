use crate::config::Config;
use crate::csv_processor::CsvRecord;
use anyhow::{Context, Result};
// use regex::Regex;
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use walkdir::WalkDir;

pub struct PathProcessor {
    config: Config,
}

impl PathProcessor {
    pub fn new(config: Config) -> Self {
        Self { config }
    }

    pub fn generate_output_structure(
        &self,
        record: &CsvRecord,
        date_components: &HashMap<String, String>,
    ) -> Result<Vec<(PathBuf, PathBuf)>> {
        let mut file_mappings = Vec::new();
        
        // 遍历模板目录
        for entry in WalkDir::new(&self.config.template_dir) {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_file() && path.extension().and_then(|s| s.to_str()) == Some("docx") {
                let relative_path = path.strip_prefix(&self.config.template_dir)?;

                let output_path = self.process_path(relative_path, record, date_components)?;
                let full_output_path = Path::new(&self.config.output_dir).join(output_path);

                file_mappings.push((path.to_path_buf(), full_output_path));
            }
        }
        
        Ok(file_mappings)
    }

    fn process_path(
        &self,
        template_path: &Path,
        record: &CsvRecord,
        date_components: &HashMap<String, String>,
    ) -> Result<PathBuf> {
        let path_str = template_path.to_string_lossy();
        let mut result = path_str.to_string();

        // 构建替换映射
        let replacements = self.build_replacements(record, date_components);

        // 替换路径中的占位符
        for (placeholder, template_var) in &self.config.path_mappings {
            if let Some(value) = replacements.get(template_var) {
                // 清理文件名中的非法字符
                let clean_value = self.sanitize_filename(value);
                result = result.replace(placeholder, &clean_value);
            }
        }

        Ok(PathBuf::from(result))
    }



    fn sanitize_filename(&self, filename: &str) -> String {
        // 移除或替换文件名中的非法字符
        let illegal_chars = ['<', '>', ':', '"', '|', '?', '*', '/', '\\'];
        let mut result = filename.to_string();
        
        for ch in illegal_chars {
            result = result.replace(ch, "_");
        }
        
        // 移除前后空格
        result.trim().to_string()
    }

    pub fn create_output_directories(&self, file_mappings: &[(PathBuf, PathBuf)]) -> Result<()> {
        for (_, output_path) in file_mappings {
            if let Some(parent) = output_path.parent() {
                std::fs::create_dir_all(parent)
                    .with_context(|| format!("无法创建目录: {}", parent.display()))?;
            }
        }
        Ok(())
    }

    pub fn build_replacements(
        &self,
        record: &CsvRecord,
        date_components: &HashMap<String, String>,
    ) -> HashMap<String, String> {
        let mut replacements = HashMap::new();

        // 添加CSV字段映射
        for (csv_column, template_var) in &self.config.column_mappings {
            let value = record.get_or_empty(csv_column);
            replacements.insert(template_var.clone(), value.clone());
            println!("    映射: {} -> {} = '{}'", csv_column, template_var, value);
        }

        // 添加日期组件
        for (key, value) in date_components {
            replacements.insert(key.clone(), value.clone());
            println!("    日期组件: {} = '{}'", key, value);
        }

        println!("    总共 {} 个替换项", replacements.len());
        replacements
    }
}
