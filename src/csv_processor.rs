use crate::config::Config;
use anyhow::{Context, Result};
use chrono::NaiveDate;
use csv::ReaderBuilder;
use encoding_rs::GBK;
use regex::Regex;
use std::collections::HashMap;
use std::fs::File;
use std::io::Read;

#[derive(Debug, Clone)]
pub struct CsvRecord {
    pub data: HashMap<String, String>,
}

impl CsvRecord {
    pub fn get(&self, key: &str) -> Option<&String> {
        self.data.get(key)
    }

    pub fn get_or_empty(&self, key: &str) -> String {
        self.data.get(key).cloned().unwrap_or_default()
    }
}

pub struct CsvProcessor {
    config: Config,
}

impl CsvProcessor {
    pub fn new(config: Config) -> Self {
        Self { config }
    }

    pub fn read_csv(&self) -> Result<Vec<CsvRecord>> {
        // 读取文件内容
        let mut file = File::open(&self.config.csv_file)
            .with_context(|| format!("无法打开CSV文件: {}", self.config.csv_file))?;
        
        let mut buffer = Vec::new();
        file.read_to_end(&mut buffer)?;

        // 尝试检测编码并转换为UTF-8
        let (content, _, _) = GBK.decode(&buffer);
        let content = content.into_owned();

        // 解析CSV
        let mut reader = ReaderBuilder::new()
            .has_headers(true)
            .from_reader(content.as_bytes());

        let headers = reader.headers()?.clone();
        let mut records = Vec::new();

        for result in reader.records() {
            let record = result?;
            let mut data = HashMap::new();

            for (i, field) in record.iter().enumerate() {
                if let Some(header) = headers.get(i) {
                    let value = self.process_field_value(header, field);
                    data.insert(header.to_string(), value);
                }
            }

            // 只处理非空记录
            if !data.is_empty() && data.values().any(|v| !v.trim().is_empty()) {
                records.push(CsvRecord { data });
            }
        }

        Ok(records)
    }

    fn process_field_value(&self, header: &str, value: &str) -> String {
        let trimmed = value.trim();
        
        // 处理空值
        if trimmed.is_empty() || trimmed.eq_ignore_ascii_case("nan") || trimmed.eq_ignore_ascii_case("null") {
            return String::new();
        }

        // 处理日期字段
        if self.config.date_format.date_columns.contains(&header.to_string()) {
            if let Ok(formatted_date) = self.format_date(trimmed) {
                return formatted_date;
            }
        }

        trimmed.to_string()
    }

    fn format_date(&self, date_str: &str) -> Result<String> {
        // 尝试解析日期（假设输入格式为YYYYMMDD）
        if date_str.len() == 8 && date_str.chars().all(|c| c.is_ascii_digit()) {
            let year: i32 = date_str[0..4].parse()?;
            let month: u32 = date_str[4..6].parse()?;
            let day: u32 = date_str[6..8].parse()?;

            if let Some(_date) = NaiveDate::from_ymd_opt(year, month, day) {
                return Ok(format!("{}年{}月{}日", year, month, day));
            }
        }

        // 如果解析失败，返回原始值
        Ok(date_str.to_string())
    }

    pub fn extract_date_components(&self, record: &CsvRecord) -> HashMap<String, String> {
        let mut components = HashMap::new();

        if let Some(date_str) = record.get("故障时间") {
            // 尝试从已格式化的日期字符串中提取组件（如"2025年4月11日"）
            if let Some(captures) = regex::Regex::new(r"(\d{4})年(\d{1,2})月(\d{1,2})日").unwrap().captures(date_str) {
                let year = captures.get(1).unwrap().as_str();
                let month = captures.get(2).unwrap().as_str();
                let day = captures.get(3).unwrap().as_str();

                components.insert("年".to_string(), year.to_string());
                components.insert("月".to_string(), month.to_string());
                components.insert("日".to_string(), day.to_string());

                println!("  提取日期组件: 年={}, 月={}, 日={}", year, month, day);
            } else if date_str.len() == 8 && date_str.chars().all(|c| c.is_ascii_digit()) {
                // 处理原始8位数字格式
                let year = &date_str[0..4];
                let month = &date_str[4..6];
                let day = &date_str[6..8];

                components.insert("年".to_string(), year.to_string());
                components.insert("月".to_string(), month.to_string());
                components.insert("日".to_string(), day.to_string());

                println!("  提取日期组件: 年={}, 月={}, 日={}", year, month, day);
            } else {
                println!("  日期格式不正确: {}", date_str);
            }
        } else {
            println!("  未找到故障时间字段");
        }

        components
    }
}
